# Lang<PERSON>low + Traefik + Authelia (MFA) — Production Stack for basiraone.nl

This production-ready stack deploys **<PERSON><PERSON>low** securely behind **Traefik** with **Authelia** for authentication + **MFA (TOTP)**.
Self-hosted with comprehensive logging for debugging user issues.

## Stack Components

- `docker-compose.yml` — Complete production stack
- `.env` — Domain configuration & Let's Encrypt email
- `authelia/configuration.yml` — Authelia configuration (MFA required)
- `authelia/users_database.yml` — User database (with argon2 hash)
- `letsencrypt/acme.json` — Let's Encrypt certificate storage
- `langflow/data/` — LangFlow persistent data
- `langflow/logs/` — Application logs
- `traefik/logs/` — Traefik access & service logs
- `scripts/` — Utility scripts (hash generation, start/stop)

## Prerequisites

- **Docker** and **Docker Compose** installed
- DNS A-records pointing to your server IP:
  - `ai.basiraone.nl`
  - `auth.basiraone.nl`
- Ports 80 and 443 open in firewall

## Configuration Overview

The stack is pre-configured for basiraone.nl domain:

```env
DOMAIN=ai.basiraone.nl
AUTH_DOMAIN=auth.basiraone.nl
LE_EMAIL=<EMAIL>
```

## Deployment Steps

### 1) Generate Admin Password Hash

Generate a secure password hash for the admin user:

```bash
./scripts/generate_authelia_hash.sh
```

Copy the generated argon2 hash and replace the placeholder in `authelia/users_database.yml` under `password:`.

### 2) Start the Stack

```bash
./scripts/up.sh
```

This will:

- Create `letsencrypt/acme.json` with proper permissions
- Start all services with Docker Compose
- Automatically obtain SSL certificates from Let's Encrypt

### 3) Access the Services

- **Authelia Portal**: `https://auth.basiraone.nl`
- **LangFlow (protected by MFA)**: `https://ai.basiraone.nl`

### 4) Initial Setup

1. Navigate to `https://auth.basiraone.nl`
2. Login with `<EMAIL>` and your password
3. **Register TOTP**: Scan the QR code with Google Authenticator or Microsoft Authenticator
4. Complete MFA setup

After setup, every login requires **password + 6-digit TOTP code**.

## Monitoring and Logs

### Application Logs

**LangFlow (application logs)**

```bash
docker logs -f langflow
```

**Traefik (proxy & access logs)**

```bash
tail -f traefik/logs/access.log
tail -f traefik/logs/traefik.log
```

**Authelia (authentication & MFA)**

```bash
docker logs -f authelia
tail -f authelia/notification.txt
```

> **Debugging Tip**: Combine Traefik **access.log** (who/when) with **Authelia logs** (auth outcome) and `docker logs langflow` (app errors) to quickly trace user issues.

### Health Checks

All services include health checks:

```bash
docker ps  # Check health status
docker compose ps  # Detailed service status
```

## Management

### Stop the Stack

```bash
./scripts/down.sh
```

### Update Services

```bash
docker compose pull
docker compose up -d
```

## Troubleshooting

- **Let's Encrypt fails**: Check DNS records, ports 80/443 open, firewall/NAT configuration
- **Authelia redirect loop**: Ensure `session.domain` is set to base domain (`basiraone.nl`, not `auth.basiraone.nl`)
- **No access after login**: Verify user is in `group: admins` or explicitly listed in access rules
- **MFA required**: Confirm `policy: two_factor` is set for the application domain

## Production Enhancements

- **Rate limiting**: Add Traefik middleware or fail2ban for brute force protection
- **SMTP**: Configure Authelia SMTP for email notifications
- **Backups**: Set up automated backups of `langflow/data` using rsync/cron
- **Monitoring**: Add Prometheus/Grafana for metrics collection
- **Log aggregation**: Configure centralized logging with ELK stack or similar

## Security Features

✅ **SSL/TLS**: Automatic Let's Encrypt certificates
✅ **MFA**: TOTP (Google/Microsoft Authenticator) + WebAuthn support
✅ **Security Headers**: HSTS, X-Frame-Options, CSP
✅ **HTTP → HTTPS**: Automatic redirect
✅ **Access Control**: Role-based access with Authelia
✅ **Health Monitoring**: Built-in health checks for all services
