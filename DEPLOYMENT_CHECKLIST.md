# Production Deployment Checklist for basiraone.nl

## Pre-Deployment Requirements

### DNS Configuration
- [ ] DNS A-record for `ai.basiraone.nl` pointing to server IP
- [ ] DNS A-record for `auth.basiraone.nl` pointing to server IP
- [ ] DNS propagation verified (use `nslookup` or `dig`)

### Server Requirements
- [ ] Docker installed (version 20.10+)
- [ ] Docker Compose installed (version 2.0+)
- [ ] Firewall configured:
  - [ ] Port 80 (HTTP) open
  - [ ] Port 443 (HTTPS) open
  - [ ] SSH port secured (non-default port recommended)
- [ ] Server timezone set to Europe/Amsterdam
- [ ] Sufficient disk space (minimum 10GB free)

### Security Preparation
- [ ] Strong admin password chosen
- [ ] Password hash generated using `./scripts/generate_authelia_hash.sh`
- [ ] Hash updated in `authelia/users_database.yml`
- [ ] Review and understand MFA setup process

## Deployment Steps

### 1. Initial Setup
- [ ] Clone/download the stack to server
- [ ] Verify all configuration files are present
- [ ] Check `.env` file contains correct basiraone.nl domains
- [ ] Verify Authelia configuration uses basiraone.nl domains

### 2. Directory Permissions
- [ ] Create required directories:
  ```bash
  mkdir -p letsencrypt traefik/logs langflow/data langflow/logs authelia
  ```
- [ ] Set proper permissions:
  ```bash
  chmod 755 scripts/*.sh
  ```

### 3. Start Services
- [ ] Run deployment script: `./scripts/up.sh`
- [ ] Verify all containers are running: `docker ps`
- [ ] Check health status: `docker compose ps`
- [ ] Monitor logs for errors: `docker logs traefik authelia langflow`

### 4. SSL Certificate Verification
- [ ] Wait for Let's Encrypt certificate generation (may take 2-3 minutes)
- [ ] Verify HTTPS access to both domains
- [ ] Check certificate validity in browser
- [ ] Confirm HTTP redirects to HTTPS

### 5. Authentication Setup
- [ ] Access `https://auth.basiraone.nl`
- [ ] Login with `<EMAIL>` and password
- [ ] Complete TOTP setup:
  - [ ] Scan QR code with authenticator app
  - [ ] Verify 6-digit code works
  - [ ] Save backup codes if provided

### 6. Application Access
- [ ] Access `https://ai.basiraone.nl`
- [ ] Verify redirect to Authelia for authentication
- [ ] Complete login with password + TOTP
- [ ] Confirm LangFlow interface loads correctly
- [ ] Test basic LangFlow functionality

## Post-Deployment Verification

### Functionality Tests
- [ ] Test logout and re-login process
- [ ] Verify MFA is required for each login
- [ ] Test session timeout behavior
- [ ] Confirm access denied for unauthorized users

### Security Verification
- [ ] Verify security headers are present:
  ```bash
  curl -I https://ai.basiraone.nl
  ```
- [ ] Check SSL rating: https://www.ssllabs.com/ssltest/
- [ ] Verify no HTTP access to protected resources
- [ ] Test with different browsers/devices

### Monitoring Setup
- [ ] Verify log files are being created:
  - [ ] `traefik/logs/access.log`
  - [ ] `traefik/logs/traefik.log`
  - [ ] `authelia/notification.txt`
- [ ] Test log rotation if configured
- [ ] Set up log monitoring alerts if needed

## Backup and Maintenance

### Initial Backup
- [ ] Backup configuration files
- [ ] Backup `letsencrypt/` directory
- [ ] Backup `langflow/data/` directory
- [ ] Document backup procedure

### Maintenance Schedule
- [ ] Plan regular updates (monthly recommended)
- [ ] Schedule certificate renewal monitoring
- [ ] Plan log cleanup/rotation
- [ ] Document rollback procedures

## Troubleshooting Checklist

### Common Issues
- [ ] If Let's Encrypt fails:
  - [ ] Verify DNS records
  - [ ] Check firewall rules
  - [ ] Ensure ports 80/443 are accessible from internet
  
- [ ] If Authelia redirect loops:
  - [ ] Verify `session.domain` is set to `basiraone.nl`
  - [ ] Check browser cookies and clear if needed
  
- [ ] If access denied after login:
  - [ ] Verify user is in `admins` group
  - [ ] Check access control rules in Authelia config

### Emergency Procedures
- [ ] Know how to stop services: `./scripts/down.sh`
- [ ] Know how to access logs: `docker logs <container>`
- [ ] Have rollback plan documented
- [ ] Know how to disable MFA temporarily if needed

## Production Hardening (Optional)

### Additional Security
- [ ] Configure fail2ban for brute force protection
- [ ] Set up rate limiting in Traefik
- [ ] Configure SMTP for Authelia notifications
- [ ] Enable audit logging
- [ ] Set up intrusion detection

### Monitoring and Alerting
- [ ] Configure uptime monitoring
- [ ] Set up log aggregation
- [ ] Configure alert notifications
- [ ] Set up performance monitoring

## Sign-off

- [ ] Deployment completed successfully
- [ ] All tests passed
- [ ] Documentation updated
- [ ] Team notified of new deployment
- [ ] Monitoring confirmed operational

**Deployed by:** ________________  
**Date:** ________________  
**Version:** ________________
