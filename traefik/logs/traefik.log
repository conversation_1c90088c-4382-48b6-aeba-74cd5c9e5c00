2025-08-18T16:22:47+02:00 INF Traefik version 3.0.4 built on 2024-07-02T13:46:37Z version=3.0.4
2025-08-18T16:22:47+02:00 INF 
Stats collection is disabled.
Help us improve Traefik by turning this feature on :)
More details on: https://doc.traefik.io/traefik/contributing/data-collection/

2025-08-18T16:22:47+02:00 INF Starting provider aggregator aggregator.ProviderAggregator
2025-08-18T16:22:47+02:00 INF Starting provider *traefik.Provider
2025-08-18T16:22:47+02:00 INF Starting provider *acme.ChallengeTLSALPN
2025-08-18T16:22:47+02:00 INF Starting provider *docker.Provider
2025-08-18T16:22:47+02:00 INF Starting provider *acme.Provider
2025-08-18T16:22:47+02:00 INF Testing certificate renew... acmeCA=https://acme-v02.api.letsencrypt.org/directory providerName=le.acme
2025-08-18T16:25:22+02:00 INF Register... providerName=le.acme
2025-08-18T16:25:38+02:00 ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [auth.basiraone.nl]: error: one or more domains had a problem:\n[auth.basiraone.nl] acme: error: 400 :: urn:ietf:params:acme:error:connection :: **************: Fetching http://auth.basiraone.nl/.well-known/acme-challenge/bPcjp-FFO2Kd96jOHkI7RT2QWesnojgOhi3C61LGdYM: Timeout during connect (likely firewall problem)\n" ACME CA=https://acme-v02.api.letsencrypt.org/directory acmeCA=https://acme-v02.api.letsencrypt.org/directory domains=["auth.basiraone.nl"] providerName=le.acme routerName=authelia@docker rule=Host(`auth.basiraone.nl`)
2025-08-18T16:31:52+02:00 INF I have to go...
2025-08-18T16:31:52+02:00 INF Stopping server gracefully
2025-08-18T16:31:52+02:00 ERR error="accept tcp [::]:443: use of closed network connection" entryPointName=websecure
2025-08-18T16:31:52+02:00 ERR error="accept tcp [::]:80: use of closed network connection" entryPointName=web
2025-08-18T16:31:52+02:00 ERR error="close tcp [::]:80: use of closed network connection" entryPointName=web
2025-08-18T16:31:52+02:00 ERR error="close tcp [::]:443: use of closed network connection" entryPointName=websecure
2025-08-18T16:31:53+02:00 INF Shutting down
2025-08-18T16:31:53+02:00 INF Server stopped
2025-08-18T16:31:53+02:00 INF Traefik version 3.0.4 built on 2024-07-02T13:46:37Z version=3.0.4
2025-08-18T16:31:53+02:00 INF 
Stats collection is disabled.
Help us improve Traefik by turning this feature on :)
More details on: https://doc.traefik.io/traefik/contributing/data-collection/

2025-08-18T16:31:53+02:00 INF Starting provider aggregator aggregator.ProviderAggregator
2025-08-18T16:31:53+02:00 INF Starting provider *acme.ChallengeTLSALPN
2025-08-18T16:31:53+02:00 INF Starting provider *docker.Provider
2025-08-18T16:31:53+02:00 INF Starting provider *traefik.Provider
2025-08-18T16:31:53+02:00 INF Starting provider *acme.Provider
2025-08-18T16:31:53+02:00 INF Testing certificate renew... acmeCA=https://acme-v02.api.letsencrypt.org/directory providerName=le.acme
2025-08-18T16:32:09+02:00 ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [auth.basiraone.nl]: error: one or more domains had a problem:\n[auth.basiraone.nl] acme: error: 400 :: urn:ietf:params:acme:error:connection :: **************: Fetching http://auth.basiraone.nl/.well-known/acme-challenge/XfZrY0XVmJGAhqfV_ZM9WwFZrvPHON_VY6lpX-_0akI: Timeout during connect (likely firewall problem)\n" ACME CA=https://acme-v02.api.letsencrypt.org/directory acmeCA=https://acme-v02.api.letsencrypt.org/directory domains=["auth.basiraone.nl"] providerName=le.acme routerName=authelia@docker rule=Host(`auth.basiraone.nl`)
2025-08-18T16:33:38+02:00 ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [auth.basiraone.nl]: error: one or more domains had a problem:\n[auth.basiraone.nl] acme: error: 400 :: urn:ietf:params:acme:error:connection :: **************: Fetching http://auth.basiraone.nl/.well-known/acme-challenge/gtO00hvyNjkse5Amw2smfswdZujkFGqPfKKmW7Yk06E: Timeout during connect (likely firewall problem)\n" ACME CA=https://acme-v02.api.letsencrypt.org/directory acmeCA=https://acme-v02.api.letsencrypt.org/directory domains=["auth.basiraone.nl"] providerName=le.acme routerName=authelia@docker rule=Host(`auth.basiraone.nl`)
2025-08-18T16:34:06+02:00 INF I have to go...
2025-08-18T16:34:06+02:00 INF Stopping server gracefully
2025-08-18T16:34:06+02:00 ERR error="accept tcp [::]:443: use of closed network connection" entryPointName=websecure
2025-08-18T16:34:06+02:00 ERR error="accept tcp [::]:80: use of closed network connection" entryPointName=web
2025-08-18T16:34:06+02:00 ERR error="close tcp [::]:443: use of closed network connection" entryPointName=websecure
2025-08-18T16:34:06+02:00 ERR error="close tcp [::]:80: use of closed network connection" entryPointName=web
2025-08-18T16:34:06+02:00 INF Server stopped
2025-08-18T16:34:06+02:00 INF Shutting down
2025-08-18T16:34:18+02:00 INF Traefik version 3.0.4 built on 2024-07-02T13:46:37Z version=3.0.4
2025-08-18T16:34:18+02:00 INF 
Stats collection is disabled.
Help us improve Traefik by turning this feature on :)
More details on: https://doc.traefik.io/traefik/contributing/data-collection/

2025-08-18T16:34:18+02:00 INF Starting provider aggregator aggregator.ProviderAggregator
2025-08-18T16:34:18+02:00 INF Starting provider *traefik.Provider
2025-08-18T16:34:18+02:00 INF Starting provider *docker.Provider
2025-08-18T16:34:18+02:00 INF Starting provider *acme.ChallengeTLSALPN
2025-08-18T16:34:18+02:00 INF Starting provider *acme.Provider
2025-08-18T16:34:18+02:00 INF Testing certificate renew... acmeCA=https://acme-v02.api.letsencrypt.org/directory providerName=le.acme
2025-08-18T16:34:41+02:00 ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [auth.basiraone.nl]: error: one or more domains had a problem:\n[auth.basiraone.nl] acme: error: 400 :: urn:ietf:params:acme:error:connection :: **************: Fetching http://auth.basiraone.nl/.well-known/acme-challenge/y5Wn7mb30r5bX2bhEuIQ86jwHriNnKt2l62cRM89iXQ: Timeout during connect (likely firewall problem)\n" ACME CA=https://acme-v02.api.letsencrypt.org/directory acmeCA=https://acme-v02.api.letsencrypt.org/directory domains=["auth.basiraone.nl"] providerName=le.acme routerName=authelia@docker rule=Host(`auth.basiraone.nl`)
2025-08-18T16:35:01+02:00 ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [auth.basiraone.nl]: error: one or more domains had a problem:\n[auth.basiraone.nl] acme: error: 400 :: urn:ietf:params:acme:error:connection :: **************: Fetching http://auth.basiraone.nl/.well-known/acme-challenge/7UCKlqRjJgNooNGCJWk_tkG1ooRq_ds1qIwR3dfYShQ: Timeout during connect (likely firewall problem)\n" ACME CA=https://acme-v02.api.letsencrypt.org/directory acmeCA=https://acme-v02.api.letsencrypt.org/directory domains=["auth.basiraone.nl"] providerName=le.acme routerName=authelia@docker rule=Host(`auth.basiraone.nl`)
2025-08-18T16:35:01+02:00 ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [ai.basiraone.nl]: error: one or more domains had a problem:\n[ai.basiraone.nl] acme: error: 400 :: urn:ietf:params:acme:error:connection :: **************: Fetching http://ai.basiraone.nl/.well-known/acme-challenge/-CvbP_MRP8meOCiVNz3Ia8PNAZkY7ztdJ0xXEkz3gXg: Timeout during connect (likely firewall problem)\n" ACME CA=https://acme-v02.api.letsencrypt.org/directory acmeCA=https://acme-v02.api.letsencrypt.org/directory domains=["ai.basiraone.nl"] providerName=le.acme routerName=langflow@docker rule=Host(`ai.basiraone.nl`)
2025-08-18T16:43:59+02:00 ERR Error while Peeking first byte error="read tcp **********:80->**********:44706: i/o timeout"
2025-08-18T16:43:59+02:00 ERR Error while Peeking first byte error="read tcp **********:80->**********:44708: i/o timeout"
2025-08-18T16:44:18+02:00 WRN A new release has been found: 3.5.0. Please consider updating.
2025-08-18T16:48:53+02:00 ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [auth.basiraone.nl]: error: one or more domains had a problem:\n[auth.basiraone.nl] acme: error: 400 :: urn:ietf:params:acme:error:connection :: **************: Fetching http://auth.basiraone.nl/.well-known/acme-challenge/DaOJTlZKHJADvMwcS9z3WsLVQqE6BMecI9sqGZeJwkE: Timeout during connect (likely firewall problem)\n" ACME CA=https://acme-v02.api.letsencrypt.org/directory acmeCA=https://acme-v02.api.letsencrypt.org/directory domains=["auth.basiraone.nl"] providerName=le.acme routerName=authelia@docker rule=Host(`auth.basiraone.nl`)
2025-08-18T16:49:07+02:00 ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [auth.basiraone.nl]: acme: error: 429 :: POST :: https://acme-v02.api.letsencrypt.org/acme/new-order :: urn:ietf:params:acme:error:rateLimited :: too many failed authorizations (5) for \"auth.basiraone.nl\" in the last 1h0m0s, retry after 2025-08-18 14:49:33 UTC: see https://letsencrypt.org/docs/rate-limits/#authorization-failures-per-hostname-per-account" ACME CA=https://acme-v02.api.letsencrypt.org/directory acmeCA=https://acme-v02.api.letsencrypt.org/directory domains=["auth.basiraone.nl"] providerName=le.acme routerName=authelia@docker rule=Host(`auth.basiraone.nl`)
2025-08-18T16:49:26+02:00 ERR Unable to obtain ACME certificate for domains error="unable to generate a certificate for the domains [ai.basiraone.nl]: error: one or more domains had a problem:\n[ai.basiraone.nl] acme: error: 400 :: urn:ietf:params:acme:error:connection :: **************: Fetching http://ai.basiraone.nl/.well-known/acme-challenge/DLNNepbWbH1FdDIBGRKncpCHUZ8E3EERnHPJLH52U40: Timeout during connect (likely firewall problem)\n" ACME CA=https://acme-v02.api.letsencrypt.org/directory acmeCA=https://acme-v02.api.letsencrypt.org/directory domains=["ai.basiraone.nl"] providerName=le.acme routerName=langflow@docker rule=Host(`ai.basiraone.nl`)
