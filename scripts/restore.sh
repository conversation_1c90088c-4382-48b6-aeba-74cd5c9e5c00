#!/usr/bin/env bash
set -euo pipefail

# Production Restore Script for basiraone.nl LangFlow Stack
# This script restores from backups created by backup.sh

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Configuration
BACKUP_DIR="/opt/backups/langflow-stack"

# Change to script directory
cd "$(dirname "$0")/.."

# Function to list available backups
list_backups() {
    echo -e "${BLUE}Available backups:${NC}"
    if [ -d "${BACKUP_DIR}" ]; then
        ls -lht "${BACKUP_DIR}"/langflow_backup_*.tar.gz 2>/dev/null | head -20 || echo "No backups found"
    else
        echo "Backup directory not found: ${BACKUP_DIR}"
    fi
}

# Function to show usage
usage() {
    echo "Usage: $0 [backup_file]"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Interactive mode - choose from list"
    echo "  $0 langflow_backup_20241218_143000.tar.gz  # Restore specific backup"
    echo ""
    list_backups
}

# Check if backup file is provided
if [ $# -eq 0 ]; then
    # Interactive mode
    list_backups
    echo ""
    read -p "Enter backup filename (or 'q' to quit): " BACKUP_FILE
    if [ "$BACKUP_FILE" = "q" ]; then
        exit 0
    fi
elif [ $# -eq 1 ]; then
    BACKUP_FILE="$1"
else
    usage
    exit 1
fi

# Validate backup file
BACKUP_PATH="${BACKUP_DIR}/${BACKUP_FILE}"
if [ ! -f "${BACKUP_PATH}" ]; then
    error "Backup file not found: ${BACKUP_PATH}"
fi

# Confirmation
echo -e "${YELLOW}WARNING: This will restore from backup and may overwrite current data!${NC}"
echo "Backup file: ${BACKUP_PATH}"
echo "Current directory: $(pwd)"
echo ""
read -p "Are you sure you want to continue? (yes/no): " CONFIRM

if [ "$CONFIRM" != "yes" ]; then
    log "Restore cancelled by user"
    exit 0
fi

# Stop services before restore
log "Stopping services..."
if [ -f "docker-compose.yml" ]; then
    docker compose down || warn "Failed to stop some services"
fi

# Create restore point of current state
log "Creating restore point of current state..."
RESTORE_POINT_DIR="/tmp/langflow_restore_point_$(date +%Y%m%d_%H%M%S)"
mkdir -p "${RESTORE_POINT_DIR}"

# Backup current state
if [ -d "authelia" ]; then cp -r authelia "${RESTORE_POINT_DIR}/"; fi
if [ -f ".env" ]; then cp .env "${RESTORE_POINT_DIR}/"; fi
if [ -f "docker-compose.yml" ]; then cp docker-compose.yml "${RESTORE_POINT_DIR}/"; fi
if [ -d "langflow/data" ]; then cp -r langflow/data "${RESTORE_POINT_DIR}/langflow_data"; fi
if [ -d "letsencrypt" ]; then cp -r letsencrypt "${RESTORE_POINT_DIR}/"; fi

log "Current state backed up to: ${RESTORE_POINT_DIR}"

# Extract backup
log "Extracting backup..."
TEMP_RESTORE_DIR="/tmp/langflow_restore_$(date +%Y%m%d_%H%M%S)"
mkdir -p "${TEMP_RESTORE_DIR}"
cd "${TEMP_RESTORE_DIR}"
tar -xzf "${BACKUP_PATH}"

# Find the extracted directory
EXTRACTED_DIR=$(find . -maxdepth 1 -type d -name "langflow_backup_*" | head -1)
if [ -z "$EXTRACTED_DIR" ]; then
    error "Could not find extracted backup directory"
fi

cd "${EXTRACTED_DIR}"
log "Backup extracted to: ${TEMP_RESTORE_DIR}/${EXTRACTED_DIR}"

# Return to original directory
cd "$(dirname "$0")/.."

# Restore files
log "Restoring configuration files..."
if [ -d "${TEMP_RESTORE_DIR}/${EXTRACTED_DIR}/authelia" ]; then
    rm -rf authelia
    cp -r "${TEMP_RESTORE_DIR}/${EXTRACTED_DIR}/authelia" .
    log "Authelia configuration restored"
fi

if [ -f "${TEMP_RESTORE_DIR}/${EXTRACTED_DIR}/.env" ]; then
    cp "${TEMP_RESTORE_DIR}/${EXTRACTED_DIR}/.env" .
    log "Environment configuration restored"
fi

if [ -f "${TEMP_RESTORE_DIR}/${EXTRACTED_DIR}/docker-compose.yml" ]; then
    cp "${TEMP_RESTORE_DIR}/${EXTRACTED_DIR}/docker-compose.yml" .
    log "Docker Compose configuration restored"
fi

if [ -d "${TEMP_RESTORE_DIR}/${EXTRACTED_DIR}/scripts" ]; then
    cp -r "${TEMP_RESTORE_DIR}/${EXTRACTED_DIR}/scripts"/* scripts/
    chmod +x scripts/*.sh
    log "Scripts restored"
fi

# Restore LangFlow data
if [ -d "${TEMP_RESTORE_DIR}/${EXTRACTED_DIR}/langflow_data" ]; then
    mkdir -p langflow
    rm -rf langflow/data
    cp -r "${TEMP_RESTORE_DIR}/${EXTRACTED_DIR}/langflow_data" langflow/data
    log "LangFlow data restored"
fi

# Restore Let's Encrypt certificates
if [ -d "${TEMP_RESTORE_DIR}/${EXTRACTED_DIR}/letsencrypt" ]; then
    rm -rf letsencrypt
    cp -r "${TEMP_RESTORE_DIR}/${EXTRACTED_DIR}/letsencrypt" .
    chmod 600 letsencrypt/acme.json 2>/dev/null || true
    log "SSL certificates restored"
fi

# Restore logs (optional)
if [ -d "${TEMP_RESTORE_DIR}/${EXTRACTED_DIR}/langflow_logs" ]; then
    mkdir -p langflow/logs
    cp -r "${TEMP_RESTORE_DIR}/${EXTRACTED_DIR}/langflow_logs"/* langflow/logs/ 2>/dev/null || true
    log "LangFlow logs restored"
fi

if [ -d "${TEMP_RESTORE_DIR}/${EXTRACTED_DIR}/traefik_logs" ]; then
    mkdir -p traefik/logs
    cp -r "${TEMP_RESTORE_DIR}/${EXTRACTED_DIR}/traefik_logs"/* traefik/logs/ 2>/dev/null || true
    log "Traefik logs restored"
fi

# Cleanup temporary directory
rm -rf "${TEMP_RESTORE_DIR}"

# Set proper permissions
log "Setting proper permissions..."
chmod 600 letsencrypt/acme.json 2>/dev/null || true
chmod +x scripts/*.sh 2>/dev/null || true

# Start services
log "Starting services..."
./scripts/up.sh

log "Restore completed successfully!"
log "Restore point saved at: ${RESTORE_POINT_DIR}"
log ""
log "Services should be starting up. Check status with:"
log "  docker compose ps"
log "  docker logs traefik"
log "  docker logs authelia"
log "  docker logs langflow"
