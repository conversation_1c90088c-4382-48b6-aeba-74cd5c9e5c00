#!/usr/bin/env bash
set -euo pipefail

# Production Monitoring Script for basiraone.nl LangFlow Stack
# This script checks the health and status of all services

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="ai.basiraone.nl"
AUTH_DOMAIN="auth.basiraone.nl"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

success() {
    echo -e "${GREEN}✓ $1${NC}"
}

fail() {
    echo -e "${RED}✗ $1${NC}"
}

# Change to script directory
cd "$(dirname "$0")/.."

echo "=========================================="
echo "LangFlow Stack Health Monitor"
echo "Domain: ${DOMAIN}"
echo "Auth Domain: ${AUTH_DOMAIN}"
echo "Time: $(date)"
echo "=========================================="
echo ""

# Check Docker and Docker Compose
info "Checking Docker environment..."
if command -v docker &> /dev/null; then
    success "Docker is installed: $(docker --version)"
else
    fail "Docker is not installed or not in PATH"
    exit 1
fi

if command -v docker compose &> /dev/null; then
    success "Docker Compose is available"
else
    fail "Docker Compose is not available"
    exit 1
fi

echo ""

# Check container status
info "Checking container status..."
if [ -f "docker-compose.yml" ]; then
    CONTAINERS=$(docker compose ps --format "table {{.Name}}\t{{.Status}}\t{{.Health}}")
    echo "$CONTAINERS"
    echo ""
    
    # Check individual containers
    for container in traefik authelia langflow; do
        if docker ps --format "{{.Names}}" | grep -q "^${container}$"; then
            STATUS=$(docker inspect --format='{{.State.Status}}' "$container")
            HEALTH=$(docker inspect --format='{{.State.Health.Status}}' "$container" 2>/dev/null || echo "no healthcheck")
            
            if [ "$STATUS" = "running" ]; then
                if [ "$HEALTH" = "healthy" ] || [ "$HEALTH" = "no healthcheck" ]; then
                    success "${container}: Running (${HEALTH})"
                else
                    warn "${container}: Running but unhealthy (${HEALTH})"
                fi
            else
                fail "${container}: Not running (${STATUS})"
            fi
        else
            fail "${container}: Container not found"
        fi
    done
else
    fail "docker-compose.yml not found"
fi

echo ""

# Check network connectivity
info "Checking network connectivity..."

# Check DNS resolution
for domain in "$DOMAIN" "$AUTH_DOMAIN"; do
    if nslookup "$domain" &> /dev/null; then
        success "DNS resolution for ${domain}"
    else
        fail "DNS resolution failed for ${domain}"
    fi
done

echo ""

# Check HTTP/HTTPS connectivity
info "Checking HTTP/HTTPS connectivity..."

# Check Authelia
if curl -s -o /dev/null -w "%{http_code}" "https://${AUTH_DOMAIN}" | grep -q "200\|302\|401"; then
    success "Authelia HTTPS endpoint responding"
else
    fail "Authelia HTTPS endpoint not responding"
fi

# Check LangFlow (should redirect to auth)
if curl -s -o /dev/null -w "%{http_code}" "https://${DOMAIN}" | grep -q "200\|302\|401"; then
    success "LangFlow HTTPS endpoint responding"
else
    fail "LangFlow HTTPS endpoint not responding"
fi

# Check HTTP to HTTPS redirect
if curl -s -o /dev/null -w "%{http_code}" "http://${DOMAIN}" | grep -q "301\|302"; then
    success "HTTP to HTTPS redirect working"
else
    warn "HTTP to HTTPS redirect may not be working"
fi

echo ""

# Check SSL certificates
info "Checking SSL certificates..."
for domain in "$DOMAIN" "$AUTH_DOMAIN"; do
    CERT_INFO=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates 2>/dev/null || echo "failed")
    
    if [ "$CERT_INFO" != "failed" ]; then
        EXPIRY=$(echo "$CERT_INFO" | grep "notAfter" | cut -d= -f2)
        EXPIRY_EPOCH=$(date -d "$EXPIRY" +%s 2>/dev/null || echo "0")
        CURRENT_EPOCH=$(date +%s)
        DAYS_LEFT=$(( (EXPIRY_EPOCH - CURRENT_EPOCH) / 86400 ))
        
        if [ $DAYS_LEFT -gt 30 ]; then
            success "${domain}: SSL certificate valid (${DAYS_LEFT} days left)"
        elif [ $DAYS_LEFT -gt 7 ]; then
            warn "${domain}: SSL certificate expires soon (${DAYS_LEFT} days left)"
        else
            error "${domain}: SSL certificate expires very soon (${DAYS_LEFT} days left)"
        fi
    else
        fail "${domain}: Could not check SSL certificate"
    fi
done

echo ""

# Check disk space
info "Checking disk space..."
DISK_USAGE=$(df -h . | tail -1 | awk '{print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -lt 80 ]; then
    success "Disk usage: ${DISK_USAGE}%"
elif [ "$DISK_USAGE" -lt 90 ]; then
    warn "Disk usage: ${DISK_USAGE}% (getting high)"
else
    error "Disk usage: ${DISK_USAGE}% (critically high)"
fi

echo ""

# Check log files
info "Checking log files..."
LOG_FILES=(
    "traefik/logs/access.log"
    "traefik/logs/traefik.log"
    "authelia/notification.txt"
)

for log_file in "${LOG_FILES[@]}"; do
    if [ -f "$log_file" ]; then
        SIZE=$(du -h "$log_file" | cut -f1)
        MODIFIED=$(stat -c %y "$log_file" | cut -d' ' -f1,2 | cut -d'.' -f1)
        success "${log_file}: ${SIZE} (last modified: ${MODIFIED})"
    else
        warn "${log_file}: Not found"
    fi
done

echo ""

# Check recent errors in logs
info "Checking for recent errors..."
ERROR_COUNT=0

# Check Traefik logs for errors
if [ -f "traefik/logs/traefik.log" ]; then
    TRAEFIK_ERRORS=$(grep -i "error\|fatal" "traefik/logs/traefik.log" | tail -5 | wc -l)
    if [ "$TRAEFIK_ERRORS" -gt 0 ]; then
        warn "Found ${TRAEFIK_ERRORS} recent errors in Traefik logs"
        ERROR_COUNT=$((ERROR_COUNT + TRAEFIK_ERRORS))
    fi
fi

# Check Docker logs for errors
for container in traefik authelia langflow; do
    if docker ps --format "{{.Names}}" | grep -q "^${container}$"; then
        CONTAINER_ERRORS=$(docker logs "$container" --since="1h" 2>&1 | grep -i "error\|fatal\|exception" | wc -l)
        if [ "$CONTAINER_ERRORS" -gt 0 ]; then
            warn "Found ${CONTAINER_ERRORS} recent errors in ${container} logs"
            ERROR_COUNT=$((ERROR_COUNT + CONTAINER_ERRORS))
        fi
    fi
done

if [ "$ERROR_COUNT" -eq 0 ]; then
    success "No recent errors found in logs"
fi

echo ""

# Summary
echo "=========================================="
if [ "$ERROR_COUNT" -eq 0 ]; then
    log "Health check completed: All systems operational"
else
    warn "Health check completed: ${ERROR_COUNT} issues found"
fi
echo "=========================================="

# Optional: Send notification if errors found (uncomment if mail is configured)
# if [ "$ERROR_COUNT" -gt 0 ]; then
#     echo "LangFlow stack health check found ${ERROR_COUNT} issues on $(hostname)" | mail -s "Health Check Alert - basiraone.nl" <EMAIL>
# fi
