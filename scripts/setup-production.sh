#!/usr/bin/env bash
set -euo pipefail

# Production Setup Script for basiraone.nl LangFlow Stack
# This script validates and sets up the production environment

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="ai.basiraone.nl"
AUTH_DOMAIN="auth.basiraone.nl"
LE_EMAIL="<EMAIL>"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

success() {
    echo -e "${GREEN}✓ $1${NC}"
}

fail() {
    echo -e "${RED}✗ $1${NC}"
}

# Change to script directory
cd "$(dirname "$0")/.."

echo "=========================================="
echo "LangFlow Production Setup for basiraone.nl"
echo "=========================================="
echo ""

# Pre-flight checks
info "Running pre-flight checks..."

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    warn "Running as root. Consider using a non-root user with Docker permissions."
fi

# Check Docker
if ! command -v docker &> /dev/null; then
    error "Docker is not installed. Please install Docker first."
fi

if ! command -v docker compose &> /dev/null; then
    error "Docker Compose is not available. Please install Docker Compose."
fi

# Check Docker daemon
if ! docker info &> /dev/null; then
    error "Docker daemon is not running or not accessible."
fi

success "Docker environment validated"

# Check DNS resolution
info "Checking DNS resolution..."
for domain in "$DOMAIN" "$AUTH_DOMAIN"; do
    if ! nslookup "$domain" &> /dev/null; then
        error "DNS resolution failed for ${domain}. Please configure DNS records first."
    fi
    success "DNS resolution for ${domain}"
done

# Check network connectivity
info "Checking network connectivity..."
if ! curl -s --connect-timeout 5 http://google.com &> /dev/null; then
    error "No internet connectivity. Required for Let's Encrypt certificates."
fi
success "Internet connectivity verified"

# Check ports
info "Checking required ports..."
for port in 80 443; do
    if ss -tuln | grep -q ":${port} "; then
        warn "Port ${port} is already in use. This may cause conflicts."
    else
        success "Port ${port} is available"
    fi
done

# Create required directories
info "Creating required directories..."
mkdir -p letsencrypt traefik/logs langflow/data langflow/logs authelia
success "Directories created"

# Set proper permissions
info "Setting proper permissions..."
chmod +x scripts/*.sh
if [ -f "letsencrypt/acme.json" ]; then
    chmod 600 letsencrypt/acme.json
fi
success "Permissions set"

# Validate configuration files
info "Validating configuration files..."

# Check .env file
if [ ! -f ".env" ]; then
    error ".env file not found. Please create it from .env.production template."
fi

# Validate .env content
if ! grep -q "DOMAIN=${DOMAIN}" .env; then
    error ".env file does not contain correct DOMAIN setting"
fi

if ! grep -q "AUTH_DOMAIN=${AUTH_DOMAIN}" .env; then
    error ".env file does not contain correct AUTH_DOMAIN setting"
fi

if ! grep -q "LE_EMAIL=${LE_EMAIL}" .env; then
    error ".env file does not contain correct LE_EMAIL setting"
fi

success ".env file validated"

# Check docker-compose.yml
if [ ! -f "docker-compose.yml" ]; then
    error "docker-compose.yml not found"
fi

# Validate docker-compose.yml
if ! docker compose config &> /dev/null; then
    error "docker-compose.yml is invalid"
fi

success "docker-compose.yml validated"

# Check Authelia configuration
if [ ! -f "authelia/configuration.yml" ]; then
    error "Authelia configuration not found"
fi

# Check if secrets are still placeholders
if grep -q "REPLACE_WITH_LONG_RANDOM" authelia/configuration.yml; then
    warn "Authelia configuration contains placeholder secrets. These should be replaced with secure values."
fi

# Check if domain is configured
if ! grep -q "basiraone.nl" authelia/configuration.yml; then
    error "Authelia configuration does not contain basiraone.nl domain"
fi

success "Authelia configuration validated"

# Check users database
if [ ! -f "authelia/users_database.yml" ]; then
    error "Authelia users database not found"
fi

# Check if password hash is set
if grep -q "YourGeneratedHashHere" authelia/users_database.yml; then
    warn "Admin password hash not set. Run './scripts/generate_authelia_hash.sh' to generate a hash."
fi

success "Authelia users database validated"

# Check if images are available
info "Checking Docker images..."
for image in traefik:v3.0 authelia/authelia:latest langflowai/langflow:latest; do
    if ! docker image inspect "$image" &> /dev/null; then
        info "Pulling image: $image"
        docker pull "$image"
    fi
    success "Image available: $image"
done

# Final validation
info "Running final validation..."

# Test docker-compose
if ! docker compose config &> /dev/null; then
    error "Docker Compose configuration is invalid"
fi

success "All pre-flight checks passed!"

echo ""
echo "=========================================="
echo "Production Setup Complete!"
echo "=========================================="
echo ""
echo "Next steps:"
echo "1. Generate admin password hash:"
echo "   ./scripts/generate_authelia_hash.sh"
echo ""
echo "2. Update the password hash in authelia/users_database.yml"
echo ""
echo "3. Start the stack:"
echo "   ./scripts/up.sh"
echo ""
echo "4. Monitor the deployment:"
echo "   ./scripts/monitor.sh"
echo ""
echo "5. Access your services:"
echo "   - Authelia: https://${AUTH_DOMAIN}"
echo "   - LangFlow: https://${DOMAIN}"
echo ""
echo "For troubleshooting, check:"
echo "   - docker compose ps"
echo "   - docker logs traefik"
echo "   - docker logs authelia"
echo "   - docker logs langflow"
echo ""
echo "=========================================="
