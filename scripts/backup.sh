#!/usr/bin/env bash
set -euo pipefail

# Production Backup Script for basiraone.nl LangFlow Stack
# This script creates backups of all critical data and configurations

# Configuration
BACKUP_DIR="/opt/backups/langflow-stack"
RETENTION_DAYS=30
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="langflow_backup_${TIMESTAMP}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Change to script directory
cd "$(dirname "$0")/.."

# Create backup directory
mkdir -p "${BACKUP_DIR}"

log "Starting backup: ${BACKUP_NAME}"

# Create temporary backup directory
TEMP_BACKUP_DIR="/tmp/${BACKUP_NAME}"
mkdir -p "${TEMP_BACKUP_DIR}"

# Backup configuration files
log "Backing up configuration files..."
cp -r authelia "${TEMP_BACKUP_DIR}/"
cp .env "${TEMP_BACKUP_DIR}/"
cp docker-compose.yml "${TEMP_BACKUP_DIR}/"
cp -r scripts "${TEMP_BACKUP_DIR}/"

# Backup LangFlow data
log "Backing up LangFlow data..."
if [ -d "langflow/data" ]; then
    cp -r langflow/data "${TEMP_BACKUP_DIR}/langflow_data"
else
    warn "LangFlow data directory not found"
fi

# Backup LangFlow logs (last 7 days only to save space)
log "Backing up recent LangFlow logs..."
if [ -d "langflow/logs" ]; then
    mkdir -p "${TEMP_BACKUP_DIR}/langflow_logs"
    find langflow/logs -name "*.log" -mtime -7 -exec cp {} "${TEMP_BACKUP_DIR}/langflow_logs/" \;
fi

# Backup Let's Encrypt certificates
log "Backing up SSL certificates..."
if [ -d "letsencrypt" ]; then
    cp -r letsencrypt "${TEMP_BACKUP_DIR}/"
else
    warn "Let's Encrypt directory not found"
fi

# Backup Traefik logs (last 7 days only)
log "Backing up recent Traefik logs..."
if [ -d "traefik/logs" ]; then
    mkdir -p "${TEMP_BACKUP_DIR}/traefik_logs"
    find traefik/logs -name "*.log" -mtime -7 -exec cp {} "${TEMP_BACKUP_DIR}/traefik_logs/" \;
fi

# Export Docker volumes (if any custom volumes exist)
log "Exporting Docker volume information..."
docker volume ls > "${TEMP_BACKUP_DIR}/docker_volumes.txt"

# Create system information snapshot
log "Creating system information snapshot..."
{
    echo "=== System Information ==="
    date
    echo "Hostname: $(hostname)"
    echo "Docker version: $(docker --version)"
    echo "Docker Compose version: $(docker compose version)"
    echo ""
    echo "=== Running Containers ==="
    docker ps
    echo ""
    echo "=== Docker Images ==="
    docker images
    echo ""
    echo "=== Disk Usage ==="
    df -h
    echo ""
    echo "=== Memory Usage ==="
    free -h
} > "${TEMP_BACKUP_DIR}/system_info.txt"

# Create backup archive
log "Creating compressed backup archive..."
cd /tmp
tar -czf "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" "${BACKUP_NAME}"

# Calculate backup size
BACKUP_SIZE=$(du -h "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" | cut -f1)
log "Backup created: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz (${BACKUP_SIZE})"

# Cleanup temporary directory
rm -rf "${TEMP_BACKUP_DIR}"

# Remove old backups
log "Cleaning up old backups (keeping last ${RETENTION_DAYS} days)..."
find "${BACKUP_DIR}" -name "langflow_backup_*.tar.gz" -mtime +${RETENTION_DAYS} -delete

# List current backups
log "Current backups:"
ls -lh "${BACKUP_DIR}"/langflow_backup_*.tar.gz | tail -10

log "Backup completed successfully!"

# Optional: Send notification (uncomment if you have mail configured)
# echo "LangFlow backup completed: ${BACKUP_NAME}.tar.gz (${BACKUP_SIZE})" | mail -s "Backup Completed - basiraone.nl" <EMAIL>
